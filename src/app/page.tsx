import Link from "next/link";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          <div className="space-y-6">
            {/* Kavia.ai Logo and Title */}
            <div className="flex items-center justify-center space-x-4 mb-6">
              <div className="relative">
                <svg
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  fill="none"
                  className="text-primary"
                >
                  {/* Main star shape matching your design */}
                  <path
                    d="M24 4L30 18L44 18L33 28L37 42L24 34L11 42L15 28L4 18L18 18L24 4Z"
                    fill="currentColor"
                  />
                  {/* Center circle */}
                  <circle cx="24" cy="24" r="4" fill="currentColor" opacity="0.8"/>
                  {/* Inner details */}
                  <path
                    d="M24 12L26 16L30 16L27 19L28 24L24 22L20 24L21 19L18 16L22 16L24 12Z"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground tracking-wide">
              KAVIA AI
            </h1>
            <h2 className="text-2xl md:text-3xl font-light text-foreground/90">
              Website UI/UX
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Multi-tenant AI chatbot platform with beautiful design matching kavia.ai branding
            </p>
            <p className="text-sm text-muted-foreground border-t border-border pt-4 mt-6">
              Last Updated on <span className="underline">July 2025</span>
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            <div className="glass-card group hover:glow-sm">
              <h3 className="text-xl font-semibold text-foreground mb-2">Demo Tenant</h3>
              <p className="text-muted-foreground mb-4">
                Explore the demo environment with sample projects
              </p>
              <Link href="/demo/projects">
                <Button className="w-full glass-button border-0 bg-primary/20 hover:bg-primary/30 text-white hover:text-white">
                  Enter Demo
                </Button>
              </Link>
            </div>

            <div className="glass-card group hover:glow-sm">
              <h3 className="text-xl font-semibold text-foreground mb-2">Test Tenant</h3>
              <p className="text-muted-foreground mb-4">
                Test environment for development and experimentation
              </p>
              <Link href="/test/projects">
                <Button className="w-full glass-button border-0 bg-primary/10 hover:bg-primary/20 text-primary hover:text-primary">
                  Enter Test
                </Button>
              </Link>
            </div>

            <div className="glass-card group hover:glow-sm">
              <h3 className="text-xl font-semibold text-foreground mb-2">Theme Demo</h3>
              <p className="text-muted-foreground mb-4">
                Showcase of the Kavia.ai theme system
              </p>
              <Link href="/theme-demo">
                <Button className="w-full glass-button border-0 bg-accent/20 hover:bg-accent/30 text-accent-foreground">
                  View Themes
                </Button>
              </Link>
            </div>
          </div>

          <div className="mt-16 space-y-6">
            <h2 className="text-2xl font-semibold text-foreground text-center">Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="glass-card">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  <h3 className="font-medium text-foreground">Kavia.ai Theme System</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Beautiful design with orange accents matching the official kavia.ai branding
                </p>
              </div>
              <div className="glass-card">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  <h3 className="font-medium text-foreground">Multi-Tenant Architecture</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Isolated environments for different organizations with secure data separation
                </p>
              </div>
              <div className="glass-card">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  <h3 className="font-medium text-foreground">Mobile-First Design</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Responsive design optimized for all devices and screen sizes
                </p>
              </div>
              <div className="glass-card">
                <div className="flex items-center mb-2">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  <h3 className="font-medium text-foreground">Modern Tech Stack</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Next.js 14+, TypeScript, Tailwind CSS with custom Kavia.ai components
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer className="mt-16 py-8 border-t border-border">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            Built with Next.js 14+, TypeScript, Tailwind CSS, and Shadcn/ui
          </p>
        </div>
      </footer>
    </div>
  );
}

interface ChatPageProps {
  params: Promise<{
    tenant: string;
    project: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function ChatPage({ params }: ChatPageProps) {
  const { tenant, project } = await params;
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="glass backdrop-blur-md border-b border-white/20 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {tenant} / {project}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <button className="glass-button text-muted-foreground hover:text-foreground border-0 px-3 py-1">
              Settings
            </button>
          </div>
        </div>
      </header>
      
      {/* Main Chat Area */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <aside className="w-64 glass border-r border-white/20 p-4 hidden md:block">
          <div className="mb-6">
            <h2 className="font-semibold text-foreground mb-2">Project Assets</h2>
            <div className="space-y-2">
              <div className="glass-button text-sm text-muted-foreground hover:text-foreground border-0 w-full text-left">Codebase</div>
              <div className="glass-button text-sm text-muted-foreground hover:text-foreground border-0 w-full text-left">Documents</div>
              <div className="glass-button text-sm text-muted-foreground hover:text-foreground border-0 w-full text-left">Figma Files</div>
            </div>
          </div>

          <div>
            <h2 className="font-semibold text-foreground mb-2">Chat History</h2>
            <div className="space-y-2">
              <div className="glass-button text-sm text-muted-foreground hover:text-foreground border-0 w-full text-left">
                Previous conversation
              </div>
            </div>
          </div>
        </aside>
        
        {/* Chat Messages */}
        <main className="flex-1 flex flex-col">
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="max-w-3xl mx-auto space-y-4">
              <div className="text-center text-muted-foreground">
                Start a conversation with Kavia AI
              </div>
            </div>
          </div>
          
          {/* Input Area */}
          <div className="glass border-t border-white/20 p-4">
            <div className="max-w-3xl mx-auto">
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="flex-1 px-4 py-2 glass border-0 bg-white/10 text-foreground placeholder:text-muted-foreground rounded-lg"
                />
                <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary px-6 py-2 border-0 glow-sm">
                  Send
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

interface ProjectsPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function ProjectsPage({ params }: ProjectsPageProps) {
  const { tenant } = await params;
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Chat with Kavia
            </h1>
            <p className="text-muted-foreground">
              Select a project to start chatting - {tenant}
            </p>
          </div>
          <ThemeToggle />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Placeholder project cards */}
          <div className="glass-card group hover:glow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Sample Project
            </h3>
            <p className="text-muted-foreground mb-4">
              A sample project to get you started with <PERSON><PERSON> AI
            </p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Last activity: 2 hours ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>

          <div className="glass-card group hover:glow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Web Development
            </h3>
            <p className="text-muted-foreground mb-4">
              Frontend and backend development assistance
            </p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Last activity: 1 day ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>

          <div className="glass-card group hover:glow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Mobile App
            </h3>
            <p className="text-muted-foreground mb-4">
              React Native and Flutter development
            </p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Last activity: 3 days ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>
        </div>
        
        <div className="mt-8">
          <button className="glass-button bg-secondary/20 hover:bg-secondary/30 text-foreground px-6 py-3 border-0">
            + Create New Project
          </button>
        </div>
      </div>
    </div>
  );
}

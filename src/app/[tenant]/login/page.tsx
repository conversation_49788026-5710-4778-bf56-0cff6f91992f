interface LoginPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function LoginPage({ params }: LoginPageProps) {
  const { tenant } = await params;
  return (
    <div className="min-h-screen flex items-center justify-center bg-background relative">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <div className="w-full max-w-md p-6">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-foreground">
            Welcome to {tenant}
          </h1>
          <p className="text-muted-foreground mt-2">
            Sign in to access your Kavia chatbot
          </p>
        </div>
        
        <div className="glass-card">
          <form className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                Email
              </label>
              <input
                type="email"
                id="email"
                className="w-full px-3 py-2 glass border-0 bg-white/10 text-foreground placeholder:text-muted-foreground rounded-lg"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-foreground mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                className="w-full px-3 py-2 glass border-0 bg-white/10 text-foreground placeholder:text-muted-foreground rounded-lg"
                placeholder="Enter your password"
              />
            </div>

            <button
              type="submit"
              className="w-full glass-button bg-primary/20 hover:bg-primary/30 text-primary py-2 px-4 border-0 glow-sm"
            >
              Sign In
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

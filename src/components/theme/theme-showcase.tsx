'use client';

import { useTheme } from './theme-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from './theme-toggle';

export function ThemeShowcase() {
  const { theme, actualTheme } = useTheme();

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <svg
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                className="text-primary"
              >
                <path
                  d="M16 2.67L20 12L29.33 12L22 18.67L24.67 28L16 22.67L7.33 28L10 18.67L2.67 12L12 12L16 2.67Z"
                  fill="currentColor"
                />
                <circle cx="16" cy="16" r="2.67" fill="currentColor" opacity="0.8"/>
                <path
                  d="M16 8L17.33 10.67L20 10.67L18 12.67L18.67 16L16 14.67L13.33 16L14 12.67L12 10.67L14.67 10.67L16 8Z"
                  fill="currentColor"
                  opacity="0.6"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-foreground mb-1">
                KAVIA AI Theme System
              </h1>
              <p className="text-muted-foreground">
                Current: <span className="text-primary font-medium">{theme}</span> |
                Actual: <span className="text-primary font-medium">{actualTheme}</span>
              </p>
            </div>
          </div>
          <ThemeToggle />
        </div>

        {/* Color Palette */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary rounded-full"></div>
              <span>Kavia.ai Color Palette</span>
            </CardTitle>
            <CardDescription>
              Orange-themed colors matching the official Kavia.ai branding
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-background border rounded-md flex items-center justify-center">
                  <span className="text-foreground text-sm">Background</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-background</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-card border rounded-md flex items-center justify-center">
                  <span className="text-card-foreground text-sm">Card</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-card</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-primary rounded-md flex items-center justify-center">
                  <span className="text-primary-foreground text-sm">Primary</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-primary</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-secondary rounded-md flex items-center justify-center">
                  <span className="text-secondary-foreground text-sm">Secondary</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-secondary</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-muted rounded-md flex items-center justify-center">
                  <span className="text-muted-foreground text-sm">Muted</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-muted</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-accent rounded-md flex items-center justify-center">
                  <span className="text-accent-foreground text-sm">Accent</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-accent</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-destructive rounded-md flex items-center justify-center">
                  <span className="text-destructive-foreground text-sm">Destructive</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-destructive</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-success rounded-md flex items-center justify-center">
                  <span className="text-success-foreground text-sm">Success</span>
                </div>
                <p className="text-xs text-muted-foreground">bg-success</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Components */}
        <Card>
          <CardHeader>
            <CardTitle>UI Components</CardTitle>
            <CardDescription>
              Common components with theme support
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Buttons */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Buttons</h3>
              <div className="flex flex-wrap gap-2">
                <Button>Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="destructive">Destructive</Button>
              </div>
            </div>

            {/* Badges */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Badges</h3>
              <div className="flex flex-wrap gap-2">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </div>

            {/* Form Elements */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Form Elements</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Enter text..." />
                <Input placeholder="Disabled input" disabled />
              </div>
            </div>

            {/* Text Variants */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Typography</h3>
              <div className="space-y-1">
                <p className="text-foreground">Primary text (foreground)</p>
                <p className="text-muted-foreground">Secondary text (muted-foreground)</p>
                <p className="text-primary">Primary colored text</p>
                <p className="text-destructive">Error text</p>
                <p className="text-success">Success text</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Theme Information */}
        <Card>
          <CardHeader>
            <CardTitle>Theme Information</CardTitle>
            <CardDescription>
              Current theme configuration and features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-foreground mb-2">Features</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>System preference detection</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Persistent storage</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Smooth transitions</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Accessibility compliant</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Official Kavia.ai design</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-foreground mb-2">Theme Options</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Light: Clean white with orange accents</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Dark: Kavia.ai brown (#231f20) with orange</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>System: Follows OS preference</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

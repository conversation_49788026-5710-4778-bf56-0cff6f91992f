# Kavia Chatbot Glassy Theme System

## Overview

The Kavia Chatbot implements a beautiful glassmorphism theme system directly inspired by kavia.ai's modern design. The theme system features:

- **Glassmorphism Design**: Beautiful glass-like components with backdrop blur effects
- **Light Theme**: Clean, glassy appearance with subtle gradients
- **Dark Theme**: Matching kavia.ai's dark theme (`#231f20` background) with glassy overlays
- **System Theme**: Automatically follows user's OS preference
- **Persistent Storage**: Theme preference is saved across sessions
- **Proper Tailwind Config**: Custom Tailwind configuration for optimal theme management

## Theme Colors

### Light Theme
- **Background**: `#ffffff` (White)
- **Foreground**: `#0f172a` (Dark slate)
- **Primary**: `#2563eb` (Blue)
- **Card**: `#ffffff` (White)
- **Border**: `#e2e8f0` (Light gray)
- **Muted**: `#f8fafc` (Very light gray)

### Dark Theme (Kavia.ai Inspired)
- **Background**: `#231f20` (Kavia.ai dark gray)
- **Foreground**: `#ffffff` (White)
- **Primary**: `#3b82f6` (Lighter blue for better contrast)
- **Card**: `#2a2626` (Slightly lighter than background)
- **Border**: `#4b5563` (Medium gray)
- **Muted**: `#374151` (Dark gray)

## Implementation

### 1. CSS Variables System

The theme system uses CSS custom properties defined in `src/app/globals.css`:

```css
:root {
  /* Light theme variables */
  --background: #ffffff;
  --foreground: #0f172a;
  /* ... other variables */
}

[data-theme="dark"] {
  /* Dark theme overrides */
  --background: #231f20;
  --foreground: #ffffff;
  /* ... other variables */
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* System dark mode */
    /* ... dark theme variables */
  }
}
```

### 2. Theme Provider

The `ThemeProvider` component manages theme state and applies theme classes:

```tsx
import { ThemeProvider } from '@/components/theme/theme-provider';

<ThemeProvider defaultTheme="system" storageKey="kavia-theme">
  {children}
</ThemeProvider>
```

### 3. Theme Toggle Components

Two theme toggle components are available:

#### Full Theme Toggle (with dropdown)
```tsx
import { ThemeToggle } from '@/components/theme/theme-toggle';

<ThemeToggle />
```

#### Simple Theme Toggle (light/dark only)
```tsx
import { SimpleThemeToggle } from '@/components/theme/theme-toggle';

<SimpleThemeToggle />
```

## Usage

### Using Theme in Components

```tsx
import { useTheme } from '@/components/theme/theme-provider';

function MyComponent() {
  const { theme, setTheme, actualTheme } = useTheme();
  
  return (
    <div className="bg-background text-foreground">
      <p>Current theme: {theme}</p>
      <p>Actual theme: {actualTheme}</p>
    </div>
  );
}
```

### CSS Classes

Use Tailwind CSS classes with the theme variables:

```tsx
<div className="bg-background text-foreground">
  <div className="bg-card border border-border rounded-lg p-4">
    <h2 className="text-foreground">Card Title</h2>
    <p className="text-muted-foreground">Card description</p>
    <button className="bg-primary text-primary-foreground">
      Primary Button
    </button>
  </div>
</div>
```

## Features

### 1. System Preference Detection
- Automatically detects user's OS theme preference
- Updates when system theme changes
- Respects user's manual theme selection

### 2. Persistent Storage
- Theme preference saved to localStorage
- Persists across browser sessions
- Uses `kavia-theme` storage key

### 3. Smooth Transitions
- All theme changes include smooth transitions
- 200ms duration for optimal UX
- Applies to colors, borders, and shadows

### 4. Accessibility
- High contrast ratios in both themes
- Screen reader friendly theme toggle
- Keyboard navigation support

## Integration Points

### Pages with Theme Toggle
- Login page: Top-right corner
- Projects page: Header area
- Chat page: Header toolbar

### Store Integration
- UI store persists theme preference
- Zustand middleware for localStorage sync
- Type-safe theme management

## Customization

### Adding New Theme Colors

1. Add CSS variables to both light and dark theme sections:
```css
:root {
  --new-color: #value;
}

[data-theme="dark"] {
  --new-color: #dark-value;
}
```

2. Add to Tailwind theme configuration:
```css
@theme inline {
  --color-new-color: var(--new-color);
}
```

3. Use in components:
```tsx
<div className="bg-new-color">Content</div>
```

### Creating Custom Theme Variants

Extend the theme system by adding new data attributes:

```css
[data-theme="custom"] {
  --background: #custom-bg;
  --foreground: #custom-fg;
}
```

## Best Practices

1. **Always use CSS variables**: Use `bg-background` instead of `bg-white`
2. **Test both themes**: Ensure components work in light and dark modes
3. **Use semantic colors**: Use `text-muted-foreground` for secondary text
4. **Maintain contrast**: Ensure accessibility standards are met
5. **Smooth transitions**: Add transition classes for theme changes

## Browser Support

- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- localStorage support for persistence
- matchMedia API for system preference detection

{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eZbnJzN6Kqr8D99u5fcJrKu3M3xQBPdxEoLw93Ej2YE=", "__NEXT_PREVIEW_MODE_ID": "ca7be4e7f29152bc70e709f293630489", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "096d32906759edc53a7f4b7781c14cf646aa1497aa6058efeadac5ef6d99f418", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0905574fb3042e1ead894c4499a83f889a03fbe6544aefd81e186468b7f2c1f4"}}}, "functions": {}, "sortedMiddleware": ["/"]}
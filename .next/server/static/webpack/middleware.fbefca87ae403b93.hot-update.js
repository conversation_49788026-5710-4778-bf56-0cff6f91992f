"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// List of valid tenants (in a real app, this would come from a database)\nconst VALID_TENANTS = [\n    'demo',\n    'acme',\n    'kavia',\n    'test'\n];\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files, API routes, Next.js internals, and special pages\n    if (pathname.startsWith('/_next') || pathname.startsWith('/api') || pathname.startsWith('/static') || pathname.startsWith('/theme-demo') || pathname === '/' || pathname.includes('.') || pathname === '/favicon.ico') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Extract tenant from the URL path\n    const pathSegments = pathname.split('/').filter(Boolean);\n    const tenant = pathSegments[0];\n    // If no tenant in path, redirect to a default tenant or show tenant selection\n    if (!tenant) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/demo/projects', request.url));\n    }\n    // Validate tenant\n    if (!VALID_TENANTS.includes(tenant)) {\n        // In a real app, you might want to show a 404 or tenant not found page\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/demo/projects', request.url));\n    }\n    // Add tenant to headers for use in components\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    response.headers.set('x-tenant', tenant);\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});